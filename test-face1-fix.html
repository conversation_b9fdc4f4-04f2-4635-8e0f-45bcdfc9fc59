<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xifu - 面1智能旋转测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-section {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
        }
        .critical {
            border-left-color: #dc3545;
        }
        .success {
            border-left-color: #28a745;
        }
        .test-steps {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .expected {
            background: #d4edda;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .expected:before {
            content: "✅ 期望结果: ";
            font-weight: bold;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .warning:before {
            content: "⚠️ 注意: ";
            font-weight: bold;
            color: #856404;
        }
        .launch-btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,123,255,0.4);
        }
        .face-colors {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }
        .face-color {
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        .face-1 { background: #ffebee; color: #c62828; border: 2px solid #f44336; }
        .face-2 { background: #e8f5e8; color: #2e7d32; border: 2px solid #4caf50; }
        .face-3 { background: #e3f2fd; color: #1565c0; border: 2px solid #2196f3; }
        .face-4 { background: #fffde7; color: #f57f17; border: 2px solid #ffeb3b; }
        .face-5 { background: #fce4ec; color: #ad1457; border: 2px solid #e91e63; }
        .face-6 { background: #e0f2f1; color: #00695c; border: 2px solid #00bcd4; }
        h1 { color: #343a40; text-align: center; margin-bottom: 30px; }
        h2 { color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        h3 { color: #6c757d; }
    </style>
</head>
<body>
    <h1>🔄 面1智能旋转专项测试</h1>
    
    <div style="text-align: center;">
        <a href="http://localhost:5173/" class="launch-btn" target="_blank">🎮 启动 Xifu 应用</a>
        <p style="color: #6c757d;">在新标签页中打开应用，然后按照下面的测试步骤进行</p>
    </div>

    <div class="test-section critical">
        <h2>🚨 关键问题验证</h2>
        <p><strong>问题:</strong> 面1(红色)仍然可以直接吸附到其他面，没有发生智能旋转</p>
        <p><strong>期望:</strong> 面1应该完全无法直接吸附，只能通过旋转使用其他面吸附</p>
        
        <div class="warning">
            请打开浏览器开发者工具(F12)查看控制台，观察调试信息
        </div>
    </div>

    <div class="test-section">
        <h2>🎨 立方体面颜色对照</h2>
        <div class="face-colors">
            <div class="face-color face-1">面1: 红色 (X+)</div>
            <div class="face-color face-2">面2: 绿色 (Y+)</div>
            <div class="face-color face-3">面3: 蓝色 (X-)</div>
            <div class="face-color face-4">面4: 黄色 (Y-)</div>
            <div class="face-color face-5">面5: 洋红色 (Z+)</div>
            <div class="face-color face-6">面6: 青色 (Z-)</div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤 1: 面1 → 面3 (180°旋转)</h2>
        <div class="test-steps">
            <ol>
                <li>创建第一个立方体作为基础</li>
                <li>创建第二个立方体</li>
                <li>将第二个立方体的<strong>红色面(面1)</strong>慢慢接近第一个立方体的<strong>蓝色面(面3)</strong></li>
                <li>观察控制台是否出现: <code>🎯 Face 1 is approaching Face 3!</code></li>
                <li>观察是否发生180°旋转</li>
                <li>确认最终是用<strong>蓝色面(面3)</strong>吸附到<strong>蓝色面(面3)</strong></li>
            </ol>
        </div>
        <div class="expected">
            应该看到立方体自动旋转180°，用自己的蓝色面(面3)吸附到目标的蓝色面(面3)
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤 2: 面1 → 面2 (90°旋转)</h2>
        <div class="test-steps">
            <ol>
                <li>将红色面(面1)接近绿色面(面2)</li>
                <li>观察控制台是否出现: <code>🎯 Face 1 is approaching Face 2!</code></li>
                <li>观察是否旋转使用黄色面(面4)吸附</li>
            </ol>
        </div>
        <div class="expected">
            应该旋转90°，用黄色面(面4)吸附到绿色面(面2)
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤 3: 面1禁止直接吸附验证</h2>
        <div class="test-steps">
            <ol>
                <li>尝试将红色面(面1)直接贴近任何其他面</li>
                <li>确认红色面不会直接吸附</li>
                <li>观察是否只有通过智能旋转才能实现吸附</li>
            </ol>
        </div>
        <div class="expected">
            红色面(面1)应该完全无法直接吸附，立方体应该保持线框预览状态
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 调试信息检查清单</h2>
        <p>在控制台中应该看到以下信息：</p>
        <ul>
            <li><code>🔍 Checking smart rotation for face 1...</code> - 开始检查智能旋转</li>
            <li><code>🎯 Face 1 is approaching Face X!</code> - 检测到面1接近</li>
            <li><code>✅ Smart rotation successful: Face 1 -> Face X</code> - 智能旋转成功</li>
            <li><code>🔄 Smart rotation snap found!</code> - 找到智能旋转吸附</li>
            <li><code>✅ Smart rotation: Face 1 -> Face X</code> - 最终确认智能旋转</li>
        </ul>
        
        <div class="warning">
            如果没有看到这些信息，说明智能旋转没有被触发，面1可能仍在直接吸附
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 完整智能旋转规则</h2>
        <ul>
            <li><strong>面1 → 面2:</strong> 旋转90°使用面4(黄色)吸附</li>
            <li><strong>面1 → 面3:</strong> 旋转180°使用面3(蓝色)吸附 ⭐ 重点测试</li>
            <li><strong>面1 → 面4:</strong> 旋转-90°使用面2(绿色)吸附</li>
            <li><strong>面1 → 面5:</strong> 旋转-90°使用面6(青色)吸附</li>
            <li><strong>面1 → 面6:</strong> 旋转90°使用面5(洋红色)吸附</li>
        </ul>
    </div>

    <div class="test-section success">
        <h2>✅ 成功标准</h2>
        <p>测试通过的标准：</p>
        <ol>
            <li>面1完全无法直接吸附到任何面</li>
            <li>面1接近其他面时会自动触发智能旋转</li>
            <li>控制台显示正确的调试信息</li>
            <li>最终吸附使用的是旋转后的正确面，而不是面1</li>
            <li>视觉上可以看到立方体的旋转动画效果</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔧 如果测试失败</h2>
        <p>如果面1仍然可以直接吸附，请检查：</p>
        <ol>
            <li><strong>刷新页面:</strong> 确保加载了最新代码</li>
            <li><strong>控制台错误:</strong> 查看是否有JavaScript错误</li>
            <li><strong>调试信息:</strong> 确认是否看到智能旋转的调试信息</li>
            <li><strong>面的识别:</strong> 确认正确识别了红色面(面1)</li>
        </ol>
        
        <p><strong>常见问题:</strong></p>
        <ul>
            <li>如果没有调试信息，说明智能旋转检测没有触发</li>
            <li>如果有调试信息但没有旋转，说明旋转计算有问题</li>
            <li>如果面1仍然直接吸附，说明常规吸附没有被正确禁用</li>
        </ul>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 面1智能旋转测试页面已加载');
            console.log('📋 请按照页面上的步骤进行测试');
            console.log('🔍 重点观察控制台中的调试信息');
            
            // 检查应用状态
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Xifu 应用运行正常，可以开始测试');
                    }
                })
                .catch(error => {
                    console.log('❌ 无法连接到应用');
                    document.querySelector('.launch-btn').style.background = 'linear-gradient(45deg, #dc3545, #c82333)';
                    document.querySelector('.launch-btn').textContent = '❌ 服务器未运行';
                });
        });
    </script>
</body>
</html>
