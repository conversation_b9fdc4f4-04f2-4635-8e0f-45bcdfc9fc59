import * as THREE from 'three';

export class SnapSystem {
    constructor() {
        this.snapDistance = 2.0; // Maximum distance for snapping detection
        this.snapTolerance = 0.1; // Tolerance for face alignment
        this.instantSnapDistance = 1.2; // Distance for instant snapping
        this.maxStretchRatio = 2.0; // Maximum stretch ratio for dual-face snapping
    }

    /**
     * Find the best snap position for a cube
     * @param {THREE.Mesh} targetCube - The cube to be positioned
     * @param {Array} existingCubes - Array of existing cubes
     * @returns {Object} - {canSnap: boolean, position: Vector3, rotation: Quaternion}
     */
    findSnapPosition(targetCube, existingCubes) {
        if (existingCubes.length === 0) {
            return {
                canSnap: false,
                position: targetCube.position.clone(),
                rotation: targetCube.quaternion.clone(),
                scale: new THREE.Vector3(1, 1, 1)
            };
        }

        let bestSnap = null;
        let minDistance = Infinity;

        // First, try smart rotation snapping for face 1
        const smartRotationSnap = this.findSmartRotationSnap(targetCube, existingCubes);
        if (smartRotationSnap.canSnap && smartRotationSnap.distance < minDistance) {
            minDistance = smartRotationSnap.distance;
            bestSnap = smartRotationSnap;
        }

        // Then try regular snapping
        for (const existingCube of existingCubes) {
            const snapResult = this.findSnapToTarget(targetCube, existingCube);
            if (snapResult.canSnap && snapResult.distance < minDistance) {
                minDistance = snapResult.distance;
                bestSnap = snapResult;
            }
        }

        // Finally, try dual-face snapping (stretching)
        const dualFaceSnap = this.findDualFaceSnap(targetCube, existingCubes);
        if (dualFaceSnap.canSnap && dualFaceSnap.distance < minDistance) {
            bestSnap = dualFaceSnap;
        }

        return bestSnap || {
            canSnap: false,
            position: targetCube.position.clone(),
            rotation: targetCube.quaternion.clone(),
            scale: new THREE.Vector3(1, 1, 1)
        };
    }

    /**
     * Find snap position between two specific cubes
     * @param {THREE.Mesh} targetCube - The cube to be positioned
     * @param {THREE.Mesh} existingCube - The existing cube to snap to
     * @returns {Object} - Snap result
     */
    findSnapToTarget(targetCube, existingCube) {
        const validSnaps = [];

        // Check all possible face combinations (exclude face 1 from regular snapping)
        for (let targetFace = 2; targetFace <= 6; targetFace++) {
            for (let existingFace = 2; existingFace <= 6; existingFace++) {
                if (this.isValidFacePair(targetFace, existingFace)) {
                    const snapResult = this.calculateSnapPosition(
                        targetCube, targetFace,
                        existingCube, existingFace
                    );
                    
                    if (snapResult.canSnap) {
                        validSnaps.push(snapResult);
                    }
                }
            }
        }

        // Return the closest valid snap
        if (validSnaps.length > 0) {
            return validSnaps.reduce((best, current) => 
                current.distance < best.distance ? current : best
            );
        }

        return { canSnap: false };
    }

    /**
     * Check if two faces can snap together based on the rules
     * @param {number} targetFace - Face number of target cube (1-6)
     * @param {number} existingFace - Face number of existing cube (1-6)
     * @returns {boolean} - Whether the faces can snap
     */
    isValidFacePair(targetFace, existingFace, allowFace1 = false) {
        // Rule 1: Face 1 (X+) cannot overlap with any face (unless explicitly allowed for smart rotation)
        if (!allowFace1 && (targetFace === 1 || existingFace === 1)) {
            return false;
        }

        // Rule 2: Faces 5 and 6 (Z±) can only snap to faces 5 and 6
        const targetIsZ = targetFace === 5 || targetFace === 6;
        const existingIsZ = existingFace === 5 || existingFace === 6;

        if (targetIsZ || existingIsZ) {
            return targetIsZ && existingIsZ;
        }

        // Other faces can snap to each other (except face 1 unless allowed)
        return true;
    }

    /**
     * Calculate the exact snap position for two faces
     * @param {THREE.Mesh} targetCube - Target cube
     * @param {number} targetFace - Target face number
     * @param {THREE.Mesh} existingCube - Existing cube
     * @param {number} existingFace - Existing face number
     * @returns {Object} - Snap calculation result
     */
    calculateSnapPosition(targetCube, targetFace, existingCube, existingFace) {
        // Get face normals in world space
        const targetNormal = this.getFaceNormal(targetCube, targetFace);
        const existingNormal = this.getFaceNormal(existingCube, existingFace);

        // For faces to snap, they should be opposite (dot product ≈ -1)
        const dot = targetNormal.dot(existingNormal);
        if (dot > -0.7) { // More lenient tolerance for better snapping
            return { canSnap: false };
        }

        // Calculate target position - position the target cube so its face touches the existing face
        const existingFaceCenter = this.getFaceCenter(existingCube, existingFace);

        // Move the target cube so that its face center aligns with existing face center
        // The offset should be in the direction opposite to the target face normal
        const targetFaceOffset = targetNormal.clone().multiplyScalar(-0.5); // Half cube size
        const newPosition = existingFaceCenter.clone().add(targetFaceOffset);

        // Calculate distance from current position
        const distance = newPosition.distanceTo(targetCube.position);

        // Check if within snap distance
        if (distance > this.snapDistance) {
            return { canSnap: false };
        }

        // Calculate rotation to align faces (for now, keep current rotation)
        const newRotation = this.calculateAlignmentRotation(targetCube, targetFace, existingNormal);

        return {
            canSnap: true,
            position: newPosition,
            rotation: newRotation,
            scale: new THREE.Vector3(1, 1, 1), // Default scale
            distance: distance,
            targetFace: targetFace,
            existingFace: existingFace,
            snapStrength: Math.max(0, 1 - (distance / this.snapDistance)) // 0-1 strength based on distance
        };
    }

    /**
     * Get face center in world coordinates
     * @param {THREE.Mesh} cube - The cube
     * @param {number} faceNumber - Face number (1-6)
     * @returns {THREE.Vector3} - World position of face center
     */
    getFaceCenter(cube, faceNumber) {
        const position = cube.position.clone();
        const normal = this.getFaceNormal(cube, faceNumber);
        normal.multiplyScalar(0.5); // Half cube size
        return position.add(normal);
    }

    /**
     * Get face normal in world coordinates
     * @param {THREE.Mesh} cube - The cube
     * @param {number} faceNumber - Face number (1-6)
     * @returns {THREE.Vector3} - World normal vector
     */
    getFaceNormal(cube, faceNumber) {
        const localNormals = {
            1: new THREE.Vector3(1, 0, 0),   // X+ (Right)
            2: new THREE.Vector3(0, 1, 0),   // Y+ (Top)
            3: new THREE.Vector3(-1, 0, 0),  // X- (Left)
            4: new THREE.Vector3(0, -1, 0),  // Y- (Bottom)
            5: new THREE.Vector3(0, 0, 1),   // Z+ (Front)
            6: new THREE.Vector3(0, 0, -1)   // Z- (Back)
        };

        const normal = localNormals[faceNumber].clone();
        normal.applyQuaternion(cube.quaternion);
        return normal;
    }

    /**
     * Calculate rotation to align a face with a target normal
     * @param {THREE.Mesh} cube - The cube to rotate
     * @param {number} _faceNumber - Face to align (unused for now)
     * @param {THREE.Vector3} _targetNormal - Target normal direction (unused for now)
     * @returns {THREE.Quaternion} - New rotation
     */
    calculateAlignmentRotation(cube, _faceNumber, _targetNormal) {
        // For now, return the current rotation
        // This could be enhanced to properly align faces
        return cube.quaternion.clone();
    }

    /**
     * Check if a position would cause overlap with existing cubes
     * @param {THREE.Vector3} position - Position to check
     * @param {Array} existingCubes - Array of existing cubes
     * @param {THREE.Mesh} excludeCube - Cube to exclude from check
     * @returns {boolean} - Whether position causes overlap
     */
    checkOverlap(position, existingCubes, excludeCube = null) {
        const minDistance = 0.9; // Slightly less than cube size to allow touching

        for (const cube of existingCubes) {
            if (cube === excludeCube) continue;
            
            const distance = position.distanceTo(cube.position);
            if (distance < minDistance) {
                return true;
            }
        }

        return false;
    }

    /**
     * Smart rotation snapping for face 1 interactions
     * When face 1 approaches other faces, rotate to use appropriate face
     */
    findSmartRotationSnap(targetCube, existingCubes) {
        const targetFace1Normal = this.getFaceNormal(targetCube, 1);

        for (const existingCube of existingCubes) {
            const distance = targetCube.position.distanceTo(existingCube.position);
            if (distance > this.snapDistance) continue;

            // Check if face 1 is approaching any face of existing cube
            for (let existingFace = 2; existingFace <= 6; existingFace++) {
                const existingFaceNormal = this.getFaceNormal(existingCube, existingFace);
                const existingFaceCenter = this.getFaceCenter(existingCube, existingFace);

                // Calculate direction from target to existing face center
                const directionToFace = existingFaceCenter.clone().sub(targetCube.position).normalize();

                // Check if face 1 is pointing towards this existing face
                const alignment = targetFace1Normal.dot(directionToFace);
                if (alignment > 0.7) { // Face 1 is approaching this face

                    // Determine which face should be used for snapping based on existing face
                    // When face 1 approaches another face, rotate 180° to use the opposite face
                    let newTargetFace;
                    switch (existingFace) {
                        case 2: // Y+ -> rotate 180° to use face 4 (Y-)
                            newTargetFace = 4;
                            break;
                        case 3: // X- -> rotate 180° to use face 3 (X-) - this is the 180° rotation
                            newTargetFace = 3;
                            break;
                        case 4: // Y- -> rotate 180° to use face 2 (Y+)
                            newTargetFace = 2;
                            break;
                        case 5: // Z+ -> rotate 180° to use face 6 (Z-)
                            newTargetFace = 6;
                            break;
                        case 6: // Z- -> rotate 180° to use face 5 (Z+)
                            newTargetFace = 5;
                            break;
                        default:
                            continue;
                    }

                    // Check if this would be a valid face pair (allow face 1 for smart rotation)
                    if (this.isValidFacePair(newTargetFace, existingFace, true)) {
                        // Calculate rotation needed
                        const newRotation = this.calculateRotationForFaceAlignment(targetCube, newTargetFace, existingFaceNormal);

                        // Calculate position with new rotation
                        const tempCube = targetCube.clone();
                        tempCube.quaternion.copy(newRotation);

                        const snapResult = this.calculateSnapPosition(tempCube, newTargetFace, existingCube, existingFace);
                        if (snapResult.canSnap) {
                            return {
                                ...snapResult,
                                rotation: newRotation,
                                isSmartRotation: true,
                                originalTargetFace: 1,
                                newTargetFace: newTargetFace
                            };
                        }
                    }
                }
            }
        }

        return { canSnap: false };
    }

    /**
     * Calculate rotation to align a specific face with a target normal
     * For face 1 smart rotation, this implements the 180° rotation logic
     */
    calculateRotationForFaceAlignment(cube, faceNumber, targetNormal) {
        // For smart rotation from face 1, we need to implement specific 180° rotations
        if (faceNumber === 3) {
            // Face 1 -> Face 3: 180° rotation around Y axis
            const rotationY = new THREE.Quaternion();
            rotationY.setFromAxisAngle(new THREE.Vector3(0, 1, 0), Math.PI);
            return cube.quaternion.clone().multiply(rotationY);
        } else if (faceNumber === 2) {
            // Face 1 -> Face 2: 90° rotation around Z axis
            const rotationZ = new THREE.Quaternion();
            rotationZ.setFromAxisAngle(new THREE.Vector3(0, 0, 1), Math.PI / 2);
            return cube.quaternion.clone().multiply(rotationZ);
        } else if (faceNumber === 4) {
            // Face 1 -> Face 4: -90° rotation around Z axis
            const rotationZ = new THREE.Quaternion();
            rotationZ.setFromAxisAngle(new THREE.Vector3(0, 0, 1), -Math.PI / 2);
            return cube.quaternion.clone().multiply(rotationZ);
        } else if (faceNumber === 5) {
            // Face 1 -> Face 5: -90° rotation around Y axis
            const rotationY = new THREE.Quaternion();
            rotationY.setFromAxisAngle(new THREE.Vector3(0, 1, 0), -Math.PI / 2);
            return cube.quaternion.clone().multiply(rotationY);
        } else if (faceNumber === 6) {
            // Face 1 -> Face 6: 90° rotation around Y axis
            const rotationY = new THREE.Quaternion();
            rotationY.setFromAxisAngle(new THREE.Vector3(0, 1, 0), Math.PI / 2);
            return cube.quaternion.clone().multiply(rotationY);
        }

        // Fallback to original method for other cases
        const currentFaceNormal = this.getFaceNormal(cube, faceNumber);
        const targetDirection = targetNormal.clone().negate();
        const quaternion = new THREE.Quaternion();
        quaternion.setFromUnitVectors(currentFaceNormal, targetDirection);
        return cube.quaternion.clone().multiply(quaternion);
    }

    /**
     * Find dual-face snapping (stretching between two cubes)
     */
    findDualFaceSnap(targetCube, existingCubes) {
        if (existingCubes.length < 2) return { canSnap: false };

        // Find if target cube is between two existing cubes
        for (let i = 0; i < existingCubes.length; i++) {
            for (let j = i + 1; j < existingCubes.length; j++) {
                const cube1 = existingCubes[i];
                const cube2 = existingCubes[j];

                const result = this.calculateDualFaceSnap(targetCube, cube1, cube2);
                if (result.canSnap) {
                    return result;
                }
            }
        }

        return { canSnap: false };
    }

    /**
     * Calculate dual-face snapping between two cubes
     */
    calculateDualFaceSnap(targetCube, cube1, cube2) {
        // Check if target cube is roughly between the two cubes
        const cube1Pos = cube1.position;
        const cube2Pos = cube2.position;
        const targetPos = targetCube.position;

        // Calculate the line between the two cubes
        const direction = cube2Pos.clone().sub(cube1Pos).normalize();
        const distance = cube1Pos.distanceTo(cube2Pos);

        // Project target position onto the line between cubes
        const toTarget = targetPos.clone().sub(cube1Pos);
        const projection = direction.clone().multiplyScalar(toTarget.dot(direction));
        const projectedPoint = cube1Pos.clone().add(projection);

        // Check if target is roughly on the line between cubes
        const distanceToLine = targetPos.distanceTo(projectedPoint);
        if (distanceToLine > 1.0) return { canSnap: false };

        // Calculate required stretch
        const gapDistance = distance - 2.0; // Subtract the size of both existing cubes (each cube is 1 unit, so 2 total)
        if (gapDistance <= 0) return { canSnap: false }; // No gap to fill

        // The stretch ratio should be the distance needed to fill the gap
        const stretchRatio = gapDistance + 1.0; // Add 1.0 for the base cube size
        if (stretchRatio > this.maxStretchRatio) return { canSnap: false };

        // Calculate position and scale
        const centerPoint = cube1Pos.clone().add(cube2Pos).multiplyScalar(0.5);
        const scale = new THREE.Vector3(1, 1, 1);

        // Determine stretch direction based on cube alignment
        if (Math.abs(direction.x) > 0.7) {
            scale.x = stretchRatio;
        } else if (Math.abs(direction.y) > 0.7) {
            scale.y = stretchRatio;
        } else if (Math.abs(direction.z) > 0.7) {
            scale.z = stretchRatio;
        }

        return {
            canSnap: true,
            position: centerPoint,
            rotation: targetCube.quaternion.clone(),
            scale: scale,
            distance: distanceToLine,
            isDualFaceSnap: true,
            stretchRatio: stretchRatio
        };
    }
}
