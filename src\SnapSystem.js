import * as THREE from 'three';

export class SnapSystem {
    constructor() {
        this.snapDistance = 2.0; // Maximum distance for snapping detection
        this.snapTolerance = 0.1; // Tolerance for face alignment
        this.instantSnapDistance = 1.2; // Distance for instant snapping
    }

    /**
     * Find the best snap position for a cube
     * @param {THREE.Mesh} targetCube - The cube to be positioned
     * @param {Array} existingCubes - Array of existing cubes
     * @returns {Object} - {canSnap: boolean, position: Vector3, rotation: Quaternion}
     */
    findSnapPosition(targetCube, existingCubes) {
        if (existingCubes.length === 0) {
            return {
                canSnap: false,
                position: targetCube.position.clone(),
                rotation: targetCube.quaternion.clone()
            };
        }

        let bestSnap = null;
        let minDistance = Infinity;

        for (const existingCube of existingCubes) {
            const snapResult = this.findSnapToTarget(targetCube, existingCube);
            if (snapResult.canSnap && snapResult.distance < minDistance) {
                minDistance = snapResult.distance;
                bestSnap = snapResult;
            }
        }

        return bestSnap || {
            canSnap: false,
            position: targetCube.position.clone(),
            rotation: targetCube.quaternion.clone()
        };
    }

    /**
     * Find snap position between two specific cubes
     * @param {THREE.Mesh} targetCube - The cube to be positioned
     * @param {THREE.Mesh} existingCube - The existing cube to snap to
     * @returns {Object} - Snap result
     */
    findSnapToTarget(targetCube, existingCube) {
        const validSnaps = [];

        // Check all possible face combinations
        for (let targetFace = 1; targetFace <= 6; targetFace++) {
            for (let existingFace = 1; existingFace <= 6; existingFace++) {
                if (this.isValidFacePair(targetFace, existingFace)) {
                    const snapResult = this.calculateSnapPosition(
                        targetCube, targetFace,
                        existingCube, existingFace
                    );
                    
                    if (snapResult.canSnap) {
                        validSnaps.push(snapResult);
                    }
                }
            }
        }

        // Return the closest valid snap
        if (validSnaps.length > 0) {
            return validSnaps.reduce((best, current) => 
                current.distance < best.distance ? current : best
            );
        }

        return { canSnap: false };
    }

    /**
     * Check if two faces can snap together based on the rules
     * @param {number} targetFace - Face number of target cube (1-6)
     * @param {number} existingFace - Face number of existing cube (1-6)
     * @returns {boolean} - Whether the faces can snap
     */
    isValidFacePair(targetFace, existingFace) {
        // Rule 1: Face 1 (X+) cannot overlap with any face
        if (targetFace === 1 || existingFace === 1) {
            return false;
        }

        // Rule 2: Faces 5 and 6 (Z±) can only snap to faces 5 and 6
        const targetIsZ = targetFace === 5 || targetFace === 6;
        const existingIsZ = existingFace === 5 || existingFace === 6;
        
        if (targetIsZ || existingIsZ) {
            return targetIsZ && existingIsZ;
        }

        // Other faces can snap to each other (except face 1)
        return true;
    }

    /**
     * Calculate the exact snap position for two faces
     * @param {THREE.Mesh} targetCube - Target cube
     * @param {number} targetFace - Target face number
     * @param {THREE.Mesh} existingCube - Existing cube
     * @param {number} existingFace - Existing face number
     * @returns {Object} - Snap calculation result
     */
    calculateSnapPosition(targetCube, targetFace, existingCube, existingFace) {
        // Get face normals in world space
        const targetNormal = this.getFaceNormal(targetCube, targetFace);
        const existingNormal = this.getFaceNormal(existingCube, existingFace);

        // For faces to snap, they should be opposite (dot product ≈ -1)
        const dot = targetNormal.dot(existingNormal);
        if (dot > -0.7) { // More lenient tolerance for better snapping
            return { canSnap: false };
        }

        // Calculate target position - position the target cube so its face touches the existing face
        const existingFaceCenter = this.getFaceCenter(existingCube, existingFace);

        // Move the target cube so that its face center aligns with existing face center
        // The offset should be in the direction opposite to the target face normal
        const targetFaceOffset = targetNormal.clone().multiplyScalar(-0.5); // Half cube size
        const newPosition = existingFaceCenter.clone().add(targetFaceOffset);

        // Calculate distance from current position
        const distance = newPosition.distanceTo(targetCube.position);

        // Check if within snap distance
        if (distance > this.snapDistance) {
            return { canSnap: false };
        }

        // Calculate rotation to align faces (for now, keep current rotation)
        const newRotation = this.calculateAlignmentRotation(targetCube, targetFace, existingNormal);

        return {
            canSnap: true,
            position: newPosition,
            rotation: newRotation,
            distance: distance,
            targetFace: targetFace,
            existingFace: existingFace,
            snapStrength: Math.max(0, 1 - (distance / this.snapDistance)) // 0-1 strength based on distance
        };
    }

    /**
     * Get face center in world coordinates
     * @param {THREE.Mesh} cube - The cube
     * @param {number} faceNumber - Face number (1-6)
     * @returns {THREE.Vector3} - World position of face center
     */
    getFaceCenter(cube, faceNumber) {
        const position = cube.position.clone();
        const normal = this.getFaceNormal(cube, faceNumber);
        normal.multiplyScalar(0.5); // Half cube size
        return position.add(normal);
    }

    /**
     * Get face normal in world coordinates
     * @param {THREE.Mesh} cube - The cube
     * @param {number} faceNumber - Face number (1-6)
     * @returns {THREE.Vector3} - World normal vector
     */
    getFaceNormal(cube, faceNumber) {
        const localNormals = {
            1: new THREE.Vector3(1, 0, 0),   // X+ (Right)
            2: new THREE.Vector3(0, 1, 0),   // Y+ (Top)
            3: new THREE.Vector3(-1, 0, 0),  // X- (Left)
            4: new THREE.Vector3(0, -1, 0),  // Y- (Bottom)
            5: new THREE.Vector3(0, 0, 1),   // Z+ (Front)
            6: new THREE.Vector3(0, 0, -1)   // Z- (Back)
        };

        const normal = localNormals[faceNumber].clone();
        normal.applyQuaternion(cube.quaternion);
        return normal;
    }

    /**
     * Calculate rotation to align a face with a target normal
     * @param {THREE.Mesh} cube - The cube to rotate
     * @param {number} _faceNumber - Face to align (unused for now)
     * @param {THREE.Vector3} _targetNormal - Target normal direction (unused for now)
     * @returns {THREE.Quaternion} - New rotation
     */
    calculateAlignmentRotation(cube, _faceNumber, _targetNormal) {
        // For now, return the current rotation
        // This could be enhanced to properly align faces
        return cube.quaternion.clone();
    }

    /**
     * Check if a position would cause overlap with existing cubes
     * @param {THREE.Vector3} position - Position to check
     * @param {Array} existingCubes - Array of existing cubes
     * @param {THREE.Mesh} excludeCube - Cube to exclude from check
     * @returns {boolean} - Whether position causes overlap
     */
    checkOverlap(position, existingCubes, excludeCube = null) {
        const minDistance = 0.9; // Slightly less than cube size to allow touching

        for (const cube of existingCubes) {
            if (cube === excludeCube) continue;
            
            const distance = position.distanceTo(cube.position);
            if (distance < minDistance) {
                return true;
            }
        }

        return false;
    }
}
