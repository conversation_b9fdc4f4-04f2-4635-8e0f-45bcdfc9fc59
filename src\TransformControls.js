import * as THREE from 'three';

export class TransformControls {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;
        
        this.isVisible = false;
        this.targetCube = null;
        this.isDragging = false;
        this.activeAxis = null;
        
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        this.createControls();
        this.setupEventListeners();
    }

    createControls() {
        // Create control group
        this.controlGroup = new THREE.Group();
        this.controlGroup.visible = false;
        this.scene.add(this.controlGroup);

        // Create axis arrows
        this.createAxisArrows();
        
        // Create rotation rings
        this.createRotationRings();
    }

    createAxisArrows() {
        const arrowLength = 1.5;
        const arrowRadius = 0.05;
        const coneHeight = 0.3;
        const coneRadius = 0.1;

        // X-axis (Red)
        this.xAxis = this.createArrow(0xff0000, arrowLength, arrowRadius, coneHeight, coneRadius);
        this.xAxis.rotation.z = -Math.PI / 2;
        this.xAxis.userData = { axis: 'x', type: 'translate' };
        this.controlGroup.add(this.xAxis);

        // Y-axis (Green)
        this.yAxis = this.createArrow(0x00ff00, arrowLength, arrowRadius, coneHeight, coneRadius);
        this.yAxis.userData = { axis: 'y', type: 'translate' };
        this.controlGroup.add(this.yAxis);

        // Z-axis (Blue)
        this.zAxis = this.createArrow(0x0000ff, arrowLength, arrowRadius, coneHeight, coneRadius);
        this.zAxis.rotation.x = Math.PI / 2;
        this.zAxis.userData = { axis: 'z', type: 'translate' };
        this.controlGroup.add(this.zAxis);
    }

    createArrow(color, length, radius, coneHeight, coneRadius) {
        const group = new THREE.Group();
        
        // Arrow shaft
        const shaftGeometry = new THREE.CylinderGeometry(radius, radius, length - coneHeight, 8);
        const shaftMaterial = new THREE.MeshLambertMaterial({ color: color });
        const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
        shaft.position.y = (length - coneHeight) / 2;
        group.add(shaft);

        // Arrow head
        const headGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 8);
        const headMaterial = new THREE.MeshLambertMaterial({ color: color });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = length - coneHeight / 2;
        group.add(head);

        return group;
    }

    createRotationRings() {
        const ringRadius = 1.2;
        const tubeRadius = 0.03;

        // X rotation ring (Red)
        this.xRotation = this.createRing(0xff0000, ringRadius, tubeRadius);
        this.xRotation.rotation.z = Math.PI / 2;
        this.xRotation.userData = { axis: 'x', type: 'rotate' };
        this.controlGroup.add(this.xRotation);

        // Y rotation ring (Green)
        this.yRotation = this.createRing(0x00ff00, ringRadius, tubeRadius);
        this.yRotation.userData = { axis: 'y', type: 'rotate' };
        this.controlGroup.add(this.yRotation);

        // Z rotation ring (Blue)
        this.zRotation = this.createRing(0x0000ff, ringRadius, tubeRadius);
        this.zRotation.rotation.x = Math.PI / 2;
        this.zRotation.userData = { axis: 'z', type: 'rotate' };
        this.controlGroup.add(this.zRotation);
    }

    createRing(color, radius, tubeRadius) {
        const geometry = new THREE.TorusGeometry(radius, tubeRadius, 8, 32);
        const material = new THREE.MeshLambertMaterial({ color: color, transparent: true, opacity: 0.8 });
        return new THREE.Mesh(geometry, material);
    }

    setupEventListeners() {
        this.renderer.domElement.addEventListener('mousedown', this.onMouseDown.bind(this));
        this.renderer.domElement.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.renderer.domElement.addEventListener('mouseup', this.onMouseUp.bind(this));
    }

    onMouseDown(event) {
        if (!this.isVisible || this.isDragging) return;

        this.updateMousePosition(event);
        this.raycaster.setFromCamera(this.mouse, this.camera);

        // Check intersection with control elements
        const controlObjects = [this.xAxis, this.yAxis, this.zAxis, this.xRotation, this.yRotation, this.zRotation];
        const intersects = this.raycaster.intersectObjects(controlObjects, true);

        if (intersects.length > 0) {
            // Prevent event from reaching camera controls
            event.stopPropagation();
            event.preventDefault();
            event.stopImmediatePropagation();

            this.isDragging = true;
            this.activeAxis = intersects[0].object.parent.userData || intersects[0].object.userData;

            // Store initial values
            this.initialMousePosition = this.mouse.clone();
            this.initialCubePosition = this.targetCube.position.clone();
            this.initialCubeRotation = this.targetCube.rotation.clone();

            console.log(`Started dragging ${this.activeAxis.type} on ${this.activeAxis.axis} axis`);
        }
    }

    onMouseMove(event) {
        if (!this.isDragging || !this.activeAxis || !this.targetCube) return;

        // Prevent event from reaching camera controls
        event.stopPropagation();
        event.preventDefault();
        event.stopImmediatePropagation();

        this.updateMousePosition(event);

        const deltaX = this.mouse.x - this.initialMousePosition.x;
        const deltaY = this.mouse.y - this.initialMousePosition.y;

        if (this.activeAxis.type === 'translate') {
            this.handleTranslation(deltaX, deltaY);
        } else if (this.activeAxis.type === 'rotate') {
            this.handleRotation(deltaX, deltaY);
        }
    }

    onMouseUp(event) {
        if (this.isDragging) {
            this.isDragging = false;
            this.activeAxis = null;
            console.log('Stopped dragging');
        }
    }

    handleTranslation(deltaX, deltaY) {
        const sensitivity = 5.0;
        const newPosition = this.initialCubePosition.clone();
        
        switch (this.activeAxis.axis) {
            case 'x':
                newPosition.x += deltaX * sensitivity;
                break;
            case 'y':
                newPosition.y += deltaY * sensitivity;
                break;
            case 'z':
                // Z movement based on camera orientation
                const cameraDirection = new THREE.Vector3();
                this.camera.getWorldDirection(cameraDirection);
                newPosition.z += (deltaX + deltaY) * sensitivity * Math.sign(cameraDirection.z);
                break;
        }
        
        this.targetCube.position.copy(newPosition);
        this.updateControlPosition();
    }

    handleRotation(deltaX, deltaY) {
        const sensitivity = 2.0;
        const newRotation = this.initialCubeRotation.clone();
        
        switch (this.activeAxis.axis) {
            case 'x':
                newRotation.x += deltaY * sensitivity;
                break;
            case 'y':
                newRotation.y += deltaX * sensitivity;
                break;
            case 'z':
                newRotation.z += deltaX * sensitivity;
                break;
        }
        
        this.targetCube.rotation.copy(newRotation);
    }

    updateMousePosition(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    }

    showControls(cube) {
        this.targetCube = cube;
        this.isVisible = true;
        this.controlGroup.visible = true;
        this.updateControlPosition();
    }

    hideControls() {
        this.targetCube = null;
        this.isVisible = false;
        this.controlGroup.visible = false;
    }

    updateControlPosition() {
        if (this.targetCube) {
            this.controlGroup.position.copy(this.targetCube.position);
        }
    }

    update() {
        if (this.isVisible && this.targetCube) {
            this.updateControlPosition();
        }
    }

    isControlActive() {
        return this.isDragging;
    }
}
