<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xifu - 新功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .feature-section {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
        }
        .feature-section.new {
            border-left-color: #28a745;
        }
        .feature-section.enhanced {
            border-left-color: #ffc107;
        }
        .test-item {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .test-steps {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .expected {
            background: #d4edda;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .expected:before {
            content: "✅ 期望结果: ";
            font-weight: bold;
            color: #155724;
        }
        .launch-btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,123,255,0.4);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .feature-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .badge-new { background: #d4edda; color: #155724; }
        .badge-enhanced { background: #fff3cd; color: #856404; }
        .badge-advanced { background: #d1ecf1; color: #0c5460; }
        h1 { color: #343a40; text-align: center; margin-bottom: 30px; }
        h2 { color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        h3 { color: #6c757d; }
    </style>
</head>
<body>
    <h1>🚀 Xifu 新功能测试指南</h1>
    
    <div style="text-align: center;">
        <a href="http://localhost:5173/" class="launch-btn" target="_blank">🎮 启动 Xifu 应用</a>
        <p style="color: #6c757d;">在新标签页中打开应用，然后按照下面的测试步骤进行验证</p>
    </div>

    <div class="feature-section new">
        <h2>🎯 功能 1: 地面放置 (0水平面)</h2>
        <div class="feature-badge badge-new">新功能</div>
        <p><strong>功能描述:</strong> 立方体现在优先放置在y=0的水平面上，提供更稳定的基础放置。</p>
        
        <div class="test-item">
            <h3>测试步骤</h3>
            <div class="test-steps">
                <ol>
                    <li>按住"Hold & Drag to Create Cube"按钮</li>
                    <li>在空白区域移动鼠标</li>
                    <li>观察立方体是否始终保持在地面上(y=0.5位置)</li>
                    <li>释放鼠标，确认立方体放置在地面</li>
                </ol>
            </div>
            <div class="expected">
                立方体应该始终贴着地面移动，底面在y=0平面上，中心在y=0.5位置
            </div>
        </div>
    </div>

    <div class="feature-section new">
        <h2>🖱️ 功能 2: 立方体选中与删除</h2>
        <div class="feature-badge badge-new">新功能</div>
        <p><strong>功能描述:</strong> 点击立方体可以选中，选中的立方体会高亮显示，可以使用Delete键删除。</p>
        
        <div class="test-item">
            <h3>选中测试</h3>
            <div class="test-steps">
                <ol>
                    <li>创建几个立方体</li>
                    <li>点击其中一个立方体</li>
                    <li>观察立方体是否变为高亮状态（发光效果）</li>
                    <li>点击另一个立方体，确认选择切换</li>
                    <li>点击空白区域，确认取消选择</li>
                </ol>
            </div>
            <div class="expected">
                被选中的立方体应该有明显的发光高亮效果，只能同时选中一个立方体
            </div>
        </div>

        <div class="test-item">
            <h3>删除测试</h3>
            <div class="test-steps">
                <ol>
                    <li>选中一个立方体</li>
                    <li>按Delete键或Backspace键</li>
                    <li>确认立方体被删除</li>
                    <li>尝试在未选中状态下按Delete键</li>
                </ol>
            </div>
            <div class="expected">
                选中的立方体应该被删除，未选中时按Delete键无效果
            </div>
        </div>
    </div>

    <div class="feature-section enhanced">
        <h2>🔄 功能 3: 智能旋转吸附</h2>
        <div class="feature-badge badge-enhanced">增强功能</div>
        <p><strong>功能描述:</strong> 当拖拽立方体的面1(红色)接近其他立方体时，会自动旋转到合适的面进行吸附。</p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>面1 → 面3 旋转</h4>
                <div class="test-steps">
                    <ol>
                        <li>创建一个基础立方体</li>
                        <li>创建第二个立方体，让红色面(面1)接近基础立方体的蓝色面(面3)</li>
                        <li>观察是否自动旋转，用蓝色面(面3)进行吸附</li>
                    </ol>
                </div>
                <div class="expected">立方体应该自动旋转，用面3吸附到目标的面3</div>
            </div>

            <div class="feature-card">
                <h4>面1 → 面2/4 旋转</h4>
                <div class="test-steps">
                    <ol>
                        <li>让红色面(面1)接近绿色面(面2)</li>
                        <li>观察是否旋转到黄色面(面4)进行吸附</li>
                        <li>让红色面(面1)接近黄色面(面4)</li>
                        <li>观察是否旋转到绿色面(面2)进行吸附</li>
                    </ol>
                </div>
                <div class="expected">立方体应该智能选择相对的面进行吸附</div>
            </div>

            <div class="feature-card">
                <h4>面1 → 面5/6 旋转</h4>
                <div class="test-steps">
                    <ol>
                        <li>让红色面(面1)接近洋红色面(面5)</li>
                        <li>观察是否旋转到青色面(面6)进行吸附</li>
                        <li>让红色面(面1)接近青色面(面6)</li>
                        <li>观察是否旋转到洋红色面(面5)进行吸附</li>
                    </ol>
                </div>
                <div class="expected">立方体应该遵循Z轴面的特殊规则进行旋转吸附</div>
            </div>
        </div>
    </div>

    <div class="feature-section enhanced">
        <h2>📏 功能 4: 双面吸附与拉伸</h2>
        <div class="feature-badge badge-advanced">高级功能</div>
        <p><strong>功能描述:</strong> 当立方体放置在两个现有立方体之间时，会自动拉伸以同时吸附两个面。</p>
        
        <div class="test-item">
            <h3>基础拉伸测试</h3>
            <div class="test-steps">
                <ol>
                    <li>创建两个立方体，让它们相距2-4个单位</li>
                    <li>创建第三个立方体</li>
                    <li>将第三个立方体拖拽到两个立方体之间的连线上</li>
                    <li>观察立方体是否自动拉伸以连接两个立方体</li>
                </ol>
            </div>
            <div class="expected">
                立方体应该在X、Y或Z方向拉伸，同时吸附到两个现有立方体的相对面
            </div>
        </div>

        <div class="test-item">
            <h3>拉伸限制测试</h3>
            <div class="test-steps">
                <ol>
                    <li>创建两个立方体，让它们相距很远(超过4个单位)</li>
                    <li>尝试在它们之间放置立方体</li>
                    <li>确认立方体不会过度拉伸(最大2倍)</li>
                    <li>如果距离太远，立方体应该恢复正常大小</li>
                </ol>
            </div>
            <div class="expected">
                当两个立方体距离过远时，拉伸功能应该被禁用，立方体保持正常大小
            </div>
        </div>
    </div>

    <div class="feature-section">
        <h2>🎮 交互增强</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>键盘快捷键</h4>
                <ul>
                    <li><kbd>Delete</kbd> / <kbd>Backspace</kbd>: 删除选中的立方体</li>
                    <li><kbd>Space</kbd>: 切换2D/3D视图</li>
                    <li><kbd>Ctrl+C</kbd>: 清除所有立方体</li>
                    <li><kbd>Ctrl+R</kbd>: 重置相机位置</li>
                    <li><kbd>Esc</kbd>: 取消当前操作</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>鼠标操作</h4>
                <ul>
                    <li><strong>左键点击</strong>: 选中立方体</li>
                    <li><strong>左键拖拽</strong>: 创建立方体</li>
                    <li><strong>右键拖拽</strong>: 旋转视角</li>
                    <li><strong>滚轮</strong>: 缩放视图</li>
                    <li><strong>中键拖拽</strong>: 平移视图</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>UI按钮</h4>
                <ul>
                    <li><strong>Switch to 2D/3D</strong>: 切换视图模式</li>
                    <li><strong>Hold & Drag to Create Cube</strong>: 创建立方体</li>
                    <li><strong>Clear Selection</strong>: 清除当前选择</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="feature-section">
        <h2>📋 完整测试清单</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div>
                <h4>基础功能</h4>
                <ul style="list-style: none; padding: 0;">
                    <li>☐ 地面放置功能</li>
                    <li>☐ 立方体选中高亮</li>
                    <li>☐ Delete键删除功能</li>
                    <li>☐ 清除选择按钮</li>
                    <li>☐ 点击空白取消选择</li>
                </ul>
            </div>
            
            <div>
                <h4>高级功能</h4>
                <ul style="list-style: none; padding: 0;">
                    <li>☐ 面1智能旋转吸附</li>
                    <li>☐ 双面吸附拉伸</li>
                    <li>☐ 拉伸限制(最大2倍)</li>
                    <li>☐ 所有面规则正确工作</li>
                    <li>☐ 控制台日志输出</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="feature-section">
        <h2>🔍 调试信息</h2>
        <p>打开浏览器开发者工具(F12)查看控制台，应该能看到以下信息：</p>
        <ul>
            <li><code>Selected cube: [cube-id]</code> - 选中立方体时</li>
            <li><code>Deleting cube: [cube-id]</code> - 删除立方体时</li>
            <li><code>Smart rotation: Face 1 -> Face X</code> - 智能旋转时</li>
            <li><code>Dual-face snap with stretch ratio: X.XX</code> - 双面吸附时</li>
            <li><code>Selection cleared</code> - 清除选择时</li>
        </ul>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Xifu 新功能测试页面已加载');
            
            // 检查应用状态
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Xifu 应用运行正常，可以开始测试新功能');
                        document.querySelector('.launch-btn').style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                    }
                })
                .catch(error => {
                    console.log('❌ 无法连接到应用');
                    document.querySelector('.launch-btn').style.background = 'linear-gradient(45deg, #dc3545, #c82333)';
                    document.querySelector('.launch-btn').textContent = '❌ 服务器未运行';
                });
        });
    </script>
</body>
</html>
