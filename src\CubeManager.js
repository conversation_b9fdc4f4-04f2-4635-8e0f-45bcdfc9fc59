import * as THREE from 'three';
import { SnapSystem } from './SnapSystem.js';
import { TransformControls } from './TransformControls.js';

export class CubeManager {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;

        this.cubes = [];
        this.isDragging = false;
        this.currentCube = null;
        this.snapSystem = new SnapSystem();

        this.raycaster = new THREE.Raycaster();
        this.plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
        this.intersection = new THREE.Vector3();

        // Add a helper plane for better 3D positioning
        this.helperPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);

        // Snap state tracking
        this.isSnapped = false;
        this.currentSnapResult = null;
        this.snapThreshold = 1.2; // Distance threshold for snapping

        // Selection and interaction
        this.selectedCube = null;
        this.isSelecting = false;

        // Ground plane for placement (y = 0)
        this.groundPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);

        // Transform controls for selected cubes
        this.transformControls = new TransformControls(scene, camera, renderer);

        this.setupMaterials();
        this.setupSelectionEvents();
    }

    setupMaterials() {
        // Materials for different faces with face numbers
        this.faceMaterials = [
            new THREE.MeshLambertMaterial({ color: 0xff0000, transparent: true, opacity: 0.8 }), // Face 1 (X+) - Red
            new THREE.MeshLambertMaterial({ color: 0x00ff00, transparent: true, opacity: 0.8 }), // Face 2 (Y+) - Green  
            new THREE.MeshLambertMaterial({ color: 0x0000ff, transparent: true, opacity: 0.8 }), // Face 3 (X-) - Blue
            new THREE.MeshLambertMaterial({ color: 0xffff00, transparent: true, opacity: 0.8 }), // Face 4 (Y-) - Yellow
            new THREE.MeshLambertMaterial({ color: 0xff00ff, transparent: true, opacity: 0.8 }), // Face 5 (Z+) - Magenta
            new THREE.MeshLambertMaterial({ color: 0x00ffff, transparent: true, opacity: 0.8 })  // Face 6 (Z-) - Cyan
        ];

        this.previewMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.5,
            wireframe: true
        });

        // Snapped preview material (more opaque, colored)
        this.snappedPreviewMaterial = this.faceMaterials.map(mat => {
            const cloned = mat.clone();
            cloned.opacity = 0.8;
            cloned.wireframe = false;
            return cloned;
        });

        // Selected cube material (highlighted)
        this.selectedMaterial = this.faceMaterials.map(mat => {
            const cloned = mat.clone();
            cloned.emissive = new THREE.Color(0x444444);
            return cloned;
        });
    }

    startDragging(mouse) {
        // Don't start dragging if transform controls are active
        if (this.transformControls.isControlActive()) {
            return;
        }

        this.isDragging = true;
        this.isSnapped = false;
        this.currentSnapResult = null;
        this.currentCube = this.createCube(true);
        this.updateCubePosition(mouse);
    }

    stopDragging() {
        if (this.isDragging && this.currentCube) {
            // Convert preview cube to solid cube
            this.scene.remove(this.currentCube);

            const finalCube = this.createCube(false);
            finalCube.position.copy(this.currentCube.position);
            finalCube.rotation.copy(this.currentCube.rotation);
            finalCube.scale.copy(this.currentCube.scale);

            // Use the current snap result if available
            if (this.isSnapped && this.currentSnapResult && this.currentSnapResult.canSnap) {
                finalCube.position.copy(this.currentSnapResult.position);
                finalCube.rotation.copy(this.currentSnapResult.rotation);
                if (this.currentSnapResult.scale) {
                    finalCube.scale.copy(this.currentSnapResult.scale);
                }
            }

            this.cubes.push(finalCube);
            this.currentCube = null;
            this.currentSnapResult = null;
        }
        this.isDragging = false;
        this.isSnapped = false;
    }

    setupSelectionEvents() {
        // Add click event for cube selection
        this.renderer.domElement.addEventListener('click', (event) => {
            if (!this.isDragging) {
                this.handleCubeSelection(event);
            }
        });

        // Add keyboard event for deletion
        document.addEventListener('keydown', (event) => {
            if (event.code === 'Delete' || event.code === 'Backspace') {
                this.deleteSelectedCube();
            }
        });
    }

    handleCubeSelection(event) {
        // Don't handle selection if transform controls are active
        if (this.transformControls.isControlActive()) {
            return;
        }

        const mouse = new THREE.Vector2();
        mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

        this.raycaster.setFromCamera(mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.cubes);

        // Clear previous selection
        if (this.selectedCube) {
            this.selectedCube.material = this.faceMaterials;
            this.transformControls.hideControls();
        }

        if (intersects.length > 0) {
            // Select the clicked cube
            this.selectedCube = intersects[0].object;
            this.selectedCube.material = this.selectedMaterial;
            this.transformControls.showControls(this.selectedCube);
            console.log('Selected cube:', this.selectedCube.userData.id);
        } else {
            // Deselect if clicking empty space
            this.selectedCube = null;
            this.transformControls.hideControls();
        }
    }

    deleteSelectedCube() {
        if (this.selectedCube) {
            console.log('Deleting cube:', this.selectedCube.userData.id);
            this.transformControls.hideControls();
            this.removeCube(this.selectedCube);
            this.selectedCube = null;
        }
    }

    clearSelection() {
        if (this.selectedCube) {
            this.selectedCube.material = this.faceMaterials;
            this.transformControls.hideControls();
            this.selectedCube = null;
            console.log('Selection cleared');
        }
    }

    updateCubePosition(mouse) {
        if (!this.isDragging || !this.currentCube) return;

        this.raycaster.setFromCamera(mouse, this.camera);

        // Get the best intersection point for cube positioning
        const intersectionPoint = this.getBestIntersectionPoint();

        if (intersectionPoint) {
            // Set the base position following mouse
            const basePosition = intersectionPoint;

            // Always update the cube position first for smooth following
            this.currentCube.position.copy(basePosition);

            // Check for snap opportunities
            const snapResult = this.snapSystem.findSnapPosition(this.currentCube, this.cubes);

            // Store the snap result for later use
            this.currentSnapResult = snapResult;

            if (snapResult.canSnap && snapResult.distance <= this.snapThreshold) {
                // Snap to the target position (瞬移效果)
                this.currentCube.position.copy(snapResult.position);
                this.currentCube.rotation.copy(snapResult.rotation);

                // Apply scaling if it's a dual-face snap
                if (snapResult.scale) {
                    this.currentCube.scale.copy(snapResult.scale);
                } else {
                    this.currentCube.scale.set(1, 1, 1);
                }

                this.currentCube.material = this.snappedPreviewMaterial;
                this.isSnapped = true;

                // Add visual feedback for snapping
                this.addSnapVisualFeedback();

                // Log special snap types
                if (snapResult.isSmartRotation) {
                    console.log(`Smart rotation: Face ${snapResult.originalTargetFace} -> Face ${snapResult.newTargetFace}`);
                }
                if (snapResult.isDualFaceSnap) {
                    console.log(`Dual-face snap with stretch ratio: ${snapResult.stretchRatio.toFixed(2)}`);
                }
            } else {
                // Follow mouse normally - keep the base position
                this.currentCube.position.copy(basePosition);
                this.currentCube.material = this.previewMaterial;
                this.currentCube.scale.set(1, 1, 1); // Reset scale
                this.isSnapped = false;

                // Remove snap visual feedback
                this.removeSnapVisualFeedback();
            }
        }
    }

    // Get the best intersection point for positioning the cube
    getBestIntersectionPoint() {
        // Strategy 1: Always try ground plane first for reliable placement
        if (this.raycaster.ray.intersectPlane(this.groundPlane, this.intersection)) {
            const point = this.intersection.clone();
            point.y = 0.5; // Half cube height above ground (y=0 plane)
            return point;
        }

        // Strategy 2: Try to intersect with existing cubes for 3D positioning
        const cubeIntersects = this.raycaster.intersectObjects(this.cubes);
        if (cubeIntersects.length > 0) {
            const point = cubeIntersects[0].point.clone();
            // Position the cube slightly above the intersection point
            const normal = cubeIntersects[0].face.normal.clone();
            normal.transformDirection(cubeIntersects[0].object.matrixWorld);
            point.add(normal.multiplyScalar(0.5)); // Half cube size offset
            return point;
        }

        // Strategy 3: Use a dynamic plane based on camera distance
        const cameraDistance = this.camera.position.length();
        const planeDistance = Math.min(cameraDistance * 0.3, 10); // Adaptive distance

        // Create a plane perpendicular to camera direction at a reasonable distance
        const cameraDirection = new THREE.Vector3();
        this.camera.getWorldDirection(cameraDirection);
        const planePoint = this.camera.position.clone().add(cameraDirection.multiplyScalar(planeDistance));

        this.helperPlane.setFromNormalAndCoplanarPoint(cameraDirection.negate(), planePoint);

        if (this.raycaster.ray.intersectPlane(this.helperPlane, this.intersection)) {
            return this.intersection.clone();
        }

        return null;
    }

    createCube(isPreview = false) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        
        let material;
        if (isPreview) {
            material = this.previewMaterial;
        } else {
            material = this.faceMaterials;
        }
        
        const cube = new THREE.Mesh(geometry, material);
        cube.castShadow = true;
        cube.receiveShadow = true;
        
        // Add face numbering data
        cube.userData = {
            faceNormals: {
                1: new THREE.Vector3(1, 0, 0),   // X+ (Right)
                2: new THREE.Vector3(0, 1, 0),   // Y+ (Top)
                3: new THREE.Vector3(-1, 0, 0),  // X- (Left)
                4: new THREE.Vector3(0, -1, 0),  // Y- (Bottom)
                5: new THREE.Vector3(0, 0, 1),   // Z+ (Front)
                6: new THREE.Vector3(0, 0, -1)   // Z- (Back)
            },
            id: Math.random().toString(36).substring(2, 11)
        };
        
        this.scene.add(cube);
        return cube;
    }

    getCubeCount() {
        return this.cubes.length;
    }

    update(mouse, _camera) {
        if (this.isDragging && !this.transformControls.isControlActive()) {
            this.updateCubePosition(mouse);
        }

        // Update transform controls
        this.transformControls.update();
    }

    // Add visual feedback for snapping
    addSnapVisualFeedback() {
        // Could add glow effect, outline, or other visual indicators
        // For now, we use the material change which is already implemented
        if (this.currentCube) {
            this.currentCube.scale.setScalar(1.05); // Slightly larger when snapped
        }
    }

    // Remove snap visual feedback
    removeSnapVisualFeedback() {
        if (this.currentCube) {
            this.currentCube.scale.setScalar(1.0); // Normal size
        }
    }

    // Get world position of a specific face center
    getFaceCenter(cube, faceNumber) {
        const position = cube.position.clone();
        const normal = cube.userData.faceNormals[faceNumber].clone();
        normal.applyQuaternion(cube.quaternion);
        normal.multiplyScalar(0.5); // Half cube size
        return position.add(normal);
    }

    // Get world normal of a specific face
    getFaceNormal(cube, faceNumber) {
        const normal = cube.userData.faceNormals[faceNumber].clone();
        normal.applyQuaternion(cube.quaternion);
        return normal;
    }

    // Remove a cube
    removeCube(cube) {
        const index = this.cubes.indexOf(cube);
        if (index > -1) {
            this.cubes.splice(index, 1);
            this.scene.remove(cube);
        }
    }

    // Clear all cubes
    clearAll() {
        this.cubes.forEach(cube => this.scene.remove(cube));
        this.cubes = [];
    }
}
