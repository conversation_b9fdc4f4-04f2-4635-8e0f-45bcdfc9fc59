import * as THREE from 'three';
import { SnapSystem } from './SnapSystem.js';

export class CubeManager {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;

        this.cubes = [];
        this.isDragging = false;
        this.currentCube = null;
        this.snapSystem = new SnapSystem();

        this.raycaster = new THREE.Raycaster();
        this.plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
        this.intersection = new THREE.Vector3();

        // Snap state tracking
        this.isSnapped = false;
        this.currentSnapResult = null;
        this.snapThreshold = 1.2; // Distance threshold for snapping

        this.setupMaterials();
    }

    setupMaterials() {
        // Materials for different faces with face numbers
        this.faceMaterials = [
            new THREE.MeshLambertMaterial({ color: 0xff0000, transparent: true, opacity: 0.8 }), // Face 1 (X+) - Red
            new THREE.MeshLambertMaterial({ color: 0x00ff00, transparent: true, opacity: 0.8 }), // Face 2 (Y+) - Green  
            new THREE.MeshLambertMaterial({ color: 0x0000ff, transparent: true, opacity: 0.8 }), // Face 3 (X-) - Blue
            new THREE.MeshLambertMaterial({ color: 0xffff00, transparent: true, opacity: 0.8 }), // Face 4 (Y-) - Yellow
            new THREE.MeshLambertMaterial({ color: 0xff00ff, transparent: true, opacity: 0.8 }), // Face 5 (Z+) - Magenta
            new THREE.MeshLambertMaterial({ color: 0x00ffff, transparent: true, opacity: 0.8 })  // Face 6 (Z-) - Cyan
        ];

        this.previewMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.5,
            wireframe: true
        });

        // Snapped preview material (more opaque, colored)
        this.snappedPreviewMaterial = this.faceMaterials.map(mat => {
            const cloned = mat.clone();
            cloned.opacity = 0.8;
            cloned.wireframe = false;
            return cloned;
        });
    }

    startDragging(mouse) {
        this.isDragging = true;
        this.isSnapped = false;
        this.currentSnapResult = null;
        this.currentCube = this.createCube(true);
        this.updateCubePosition(mouse);
    }

    stopDragging() {
        if (this.isDragging && this.currentCube) {
            // Convert preview cube to solid cube
            this.scene.remove(this.currentCube);

            const finalCube = this.createCube(false);
            finalCube.position.copy(this.currentCube.position);
            finalCube.rotation.copy(this.currentCube.rotation);

            // Use the current snap result if available
            if (this.isSnapped && this.currentSnapResult && this.currentSnapResult.canSnap) {
                finalCube.position.copy(this.currentSnapResult.position);
                finalCube.rotation.copy(this.currentSnapResult.rotation);
            }

            this.cubes.push(finalCube);
            this.currentCube = null;
            this.currentSnapResult = null;
        }
        this.isDragging = false;
        this.isSnapped = false;
    }

    updateCubePosition(mouse) {
        if (!this.isDragging || !this.currentCube) return;

        this.raycaster.setFromCamera(mouse, this.camera);

        // Intersect with ground plane
        if (this.raycaster.ray.intersectPlane(this.plane, this.intersection)) {
            // Set the base position following mouse
            const basePosition = this.intersection.clone();
            basePosition.y = 0.5; // Half cube height above ground

            // Check for snap opportunities
            this.currentCube.position.copy(basePosition);
            const snapResult = this.snapSystem.findSnapPosition(this.currentCube, this.cubes);

            // Store the snap result for later use
            this.currentSnapResult = snapResult;

            if (snapResult.canSnap && snapResult.distance <= this.snapThreshold) {
                // Snap to the target position (瞬移效果)
                this.currentCube.position.copy(snapResult.position);
                this.currentCube.rotation.copy(snapResult.rotation);
                this.currentCube.material = this.snappedPreviewMaterial;
                this.isSnapped = true;

                // Add visual feedback for snapping
                this.addSnapVisualFeedback();
            } else {
                // Follow mouse normally
                this.currentCube.position.copy(basePosition);
                this.currentCube.material = this.previewMaterial;
                this.isSnapped = false;

                // Remove snap visual feedback
                this.removeSnapVisualFeedback();
            }
        }
    }

    createCube(isPreview = false) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        
        let material;
        if (isPreview) {
            material = this.previewMaterial;
        } else {
            material = this.faceMaterials;
        }
        
        const cube = new THREE.Mesh(geometry, material);
        cube.castShadow = true;
        cube.receiveShadow = true;
        
        // Add face numbering data
        cube.userData = {
            faceNormals: {
                1: new THREE.Vector3(1, 0, 0),   // X+ (Right)
                2: new THREE.Vector3(0, 1, 0),   // Y+ (Top)
                3: new THREE.Vector3(-1, 0, 0),  // X- (Left)
                4: new THREE.Vector3(0, -1, 0),  // Y- (Bottom)
                5: new THREE.Vector3(0, 0, 1),   // Z+ (Front)
                6: new THREE.Vector3(0, 0, -1)   // Z- (Back)
            },
            id: Math.random().toString(36).substr(2, 9)
        };
        
        this.scene.add(cube);
        return cube;
    }

    getCubeCount() {
        return this.cubes.length;
    }

    update(mouse, camera) {
        if (this.isDragging) {
            this.updateCubePosition(mouse);
        }
    }

    // Add visual feedback for snapping
    addSnapVisualFeedback() {
        // Could add glow effect, outline, or other visual indicators
        // For now, we use the material change which is already implemented
        if (this.currentCube) {
            this.currentCube.scale.setScalar(1.05); // Slightly larger when snapped
        }
    }

    // Remove snap visual feedback
    removeSnapVisualFeedback() {
        if (this.currentCube) {
            this.currentCube.scale.setScalar(1.0); // Normal size
        }
    }

    // Get world position of a specific face center
    getFaceCenter(cube, faceNumber) {
        const position = cube.position.clone();
        const normal = cube.userData.faceNormals[faceNumber].clone();
        normal.applyQuaternion(cube.quaternion);
        normal.multiplyScalar(0.5); // Half cube size
        return position.add(normal);
    }

    // Get world normal of a specific face
    getFaceNormal(cube, faceNumber) {
        const normal = cube.userData.faceNormals[faceNumber].clone();
        normal.applyQuaternion(cube.quaternion);
        return normal;
    }

    // Remove a cube
    removeCube(cube) {
        const index = this.cubes.indexOf(cube);
        if (index > -1) {
            this.cubes.splice(index, 1);
            this.scene.remove(cube);
        }
    }

    // Clear all cubes
    clearAll() {
        this.cubes.forEach(cube => this.scene.remove(cube));
        this.cubes = [];
    }
}
