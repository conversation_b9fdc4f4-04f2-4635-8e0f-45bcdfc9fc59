<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xifu - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #4CAF50;
            background: #f9f9f9;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            margin: 8px 0;
            padding: 8px;
            background: #e8f5e8;
            border-radius: 4px;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }
        .launch-btn {
            display: inline-block;
            padding: 15px 30px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            margin: 20px 0;
            transition: background 0.3s;
        }
        .launch-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>🎯 Xifu - Three.js 立方体吸附系统</h1>
    
    <div class="test-section">
        <h2>🚀 启动应用</h2>
        <p>点击下面的按钮启动主应用程序：</p>
        <a href="http://localhost:5173/" class="launch-btn" target="_blank">启动 Xifu 应用</a>
    </div>

    <div class="test-section">
        <h2>✨ 核心功能</h2>
        <ul class="feature-list">
            <li>2D/3D场景切换</li>
            <li>拖拽创建立方体</li>
            <li>智能面对面吸附</li>
            <li>立方体面编号系统 (1-6)</li>
            <li>实时预览和吸附提示</li>
            <li>碰撞检测和重叠防护</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🎮 操作指南</h2>
        <div class="test-item">
            <strong>创建立方体:</strong> 按住"Hold & Drag to Create Cube"按钮并拖拽鼠标
        </div>
        <div class="test-item">
            <strong>切换视图:</strong> 点击"Switch to 2D/3D"按钮或按空格键
        </div>
        <div class="test-item">
            <strong>相机控制:</strong> 鼠标滚轮缩放，右键拖拽旋转，中键拖拽平移
        </div>
        <div class="test-item">
            <strong>快捷键:</strong> Space(切换视图), Ctrl+C(清除), Ctrl+R(重置), Esc(取消)
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 吸附规则</h2>
        <div class="test-item">
            <strong>面1限制:</strong> X轴正方向的面(红色)不能与任何面重合
        </div>
        <div class="test-item">
            <strong>Z轴面限制:</strong> 面5和面6(洋红色/青色)只能与其他面5/6重合
        </div>
        <div class="test-item">
            <strong>其他面:</strong> 面2、3、4可以与除面1外的其他面吸附
        </div>
    </div>

    <div class="test-section">
        <h2>🎨 立方体面编号</h2>
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
            <div style="padding: 10px; background: #ffebee; border-left: 4px solid #f44336;">
                <strong>面1:</strong> X+ (右侧) - 红色
            </div>
            <div style="padding: 10px; background: #e8f5e8; border-left: 4px solid #4caf50;">
                <strong>面2:</strong> Y+ (顶部) - 绿色
            </div>
            <div style="padding: 10px; background: #e3f2fd; border-left: 4px solid #2196f3;">
                <strong>面3:</strong> X- (左侧) - 蓝色
            </div>
            <div style="padding: 10px; background: #fffde7; border-left: 4px solid #ffeb3b;">
                <strong>面4:</strong> Y- (底部) - 黄色
            </div>
            <div style="padding: 10px; background: #fce4ec; border-left: 4px solid #e91e63;">
                <strong>面5:</strong> Z+ (前面) - 洋红色
            </div>
            <div style="padding: 10px; background: #e0f2f1; border-left: 4px solid #00bcd4;">
                <strong>面6:</strong> Z- (后面) - 青色
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 测试建议</h2>
        <ol>
            <li>首先创建一个立方体作为基础</li>
            <li>尝试在不同位置创建第二个立方体，观察吸附效果</li>
            <li>测试面1(红色面)的限制规则</li>
            <li>测试面5/6(洋红色/青色面)的特殊吸附规则</li>
            <li>在2D和3D视图之间切换，观察不同视角</li>
            <li>使用键盘快捷键测试各种功能</li>
        </ol>
    </div>

    <script>
        // 简单的页面交互
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Xifu Test Page Loaded');
            
            // 检查服务器状态
            fetch('http://localhost:5173/')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Xifu 应用服务器运行正常');
                    } else {
                        console.log('⚠️ 服务器响应异常');
                    }
                })
                .catch(error => {
                    console.log('❌ 无法连接到服务器，请确保运行 npm run dev');
                });
        });
    </script>
</body>
</html>
