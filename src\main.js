import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { CubeManager } from './CubeManager.js';
import { UIController } from './UIController.js';

class XifuApp {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.cubeManager = null;
        this.uiController = null;
        
        this.is3D = true;
        this.mouse = new THREE.Vector2();
        this.raycaster = new THREE.Raycaster();
        
        this.init();
    }

    init() {
        this.setupScene();
        this.setupCamera();
        this.setupRenderer();
        this.setupControls();
        this.setupLighting();
        this.setupManagers();
        this.setupEventListeners();
        this.animate();
    }

    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);
        
        // Add grid helper
        const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x444444);
        this.scene.add(gridHelper);
        
        // Add axes helper
        const axesHelper = new THREE.AxesHelper(5);
        this.scene.add(axesHelper);
    }

    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(10, 10, 10);
        this.camera.lookAt(0, 0, 0);
    }

    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        const container = document.getElementById('canvas-container');
        container.appendChild(this.renderer.domElement);
    }

    setupControls() {
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 3;
        this.controls.maxDistance = 50;
        this.controls.maxPolarAngle = Math.PI / 2;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        this.scene.add(directionalLight);

        // Point light for better illumination
        const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
        pointLight.position.set(-10, 10, -10);
        this.scene.add(pointLight);
    }

    setupManagers() {
        this.cubeManager = new CubeManager(this.scene, this.camera, this.renderer);
        this.uiController = new UIController(this);
    }

    setupEventListeners() {
        window.addEventListener('resize', this.onWindowResize.bind(this));

        // Listen for mouse move on the entire document, not just canvas
        document.addEventListener('mousemove', this.onMouseMove.bind(this));

        // Update debug info
        this.updateDebugInfo();
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    onMouseMove(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // Update debug info
        document.getElementById('mouse-pos').textContent = 
            `${Math.round(this.mouse.x * 100) / 100}, ${Math.round(this.mouse.y * 100) / 100}`;
    }

    toggleView() {
        this.is3D = !this.is3D;
        
        if (this.is3D) {
            // Switch to 3D
            this.camera.position.set(10, 10, 10);
            this.camera.lookAt(0, 0, 0);
            this.controls.enabled = true;
            document.getElementById('toggle-view').textContent = 'Switch to 2D';
            document.getElementById('current-mode').textContent = '3D';
        } else {
            // Switch to 2D (top-down view)
            this.camera.position.set(0, 20, 0);
            this.camera.lookAt(0, 0, 0);
            this.controls.enabled = false;
            document.getElementById('toggle-view').textContent = 'Switch to 3D';
            document.getElementById('current-mode').textContent = '2D';
        }
    }

    updateDebugInfo() {
        const cubeCount = this.cubeManager ? this.cubeManager.getCubeCount() : 0;
        document.getElementById('cube-count').textContent = cubeCount;
    }

    animate() {
        requestAnimationFrame(this.animate.bind(this));
        
        this.controls.update();
        this.cubeManager.update(this.mouse, this.camera);
        this.updateDebugInfo();
        
        this.renderer.render(this.scene, this.camera);
    }
}

// Initialize the application
new XifuApp();
