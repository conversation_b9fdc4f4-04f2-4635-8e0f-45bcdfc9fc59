// Xifu Demo Script - 演示核心功能
// 这个脚本可以在浏览器控制台中运行，用于测试和演示功能

class XifuDemo {
    constructor() {
        this.app = null;
        this.demoSteps = [
            {
                name: "初始化检查",
                description: "检查应用是否正确加载",
                action: () => this.checkInitialization()
            },
            {
                name: "创建第一个立方体",
                description: "在原点附近创建一个立方体",
                action: () => this.createFirstCube()
            },
            {
                name: "测试吸附功能",
                description: "创建第二个立方体并测试吸附",
                action: () => this.testSnapping()
            },
            {
                name: "测试面1限制",
                description: "验证面1不能与其他面重合的规则",
                action: () => this.testFace1Restriction()
            },
            {
                name: "测试Z轴面限制",
                description: "验证面5/6只能与面5/6重合的规则",
                action: () => this.testZAxisRestriction()
            },
            {
                name: "切换视图",
                description: "在2D和3D视图之间切换",
                action: () => this.testViewToggle()
            }
        ];
        this.currentStep = 0;
    }

    // 检查应用初始化状态
    checkInitialization() {
        console.log("🔍 检查 Xifu 应用状态...");
        
        // 检查全局变量和DOM元素
        const canvasContainer = document.getElementById('canvas-container');
        const createBtn = document.getElementById('create-cube');
        const toggleBtn = document.getElementById('toggle-view');
        
        if (!canvasContainer) {
            console.error("❌ 未找到 canvas 容器");
            return false;
        }
        
        if (!createBtn || !toggleBtn) {
            console.error("❌ 未找到 UI 控制按钮");
            return false;
        }
        
        const canvas = canvasContainer.querySelector('canvas');
        if (!canvas) {
            console.error("❌ 未找到 Three.js canvas");
            return false;
        }
        
        console.log("✅ 应用初始化检查通过");
        console.log(`📊 Canvas 尺寸: ${canvas.width} x ${canvas.height}`);
        
        return true;
    }

    // 模拟创建第一个立方体
    createFirstCube() {
        console.log("🎯 创建第一个立方体...");
        
        const createBtn = document.getElementById('create-cube');
        if (!createBtn) {
            console.error("❌ 未找到创建按钮");
            return false;
        }
        
        // 模拟鼠标事件
        console.log("🖱️ 模拟鼠标按下事件...");
        createBtn.dispatchEvent(new MouseEvent('mousedown', {
            button: 0,
            clientX: 400,
            clientY: 300
        }));
        
        setTimeout(() => {
            console.log("🖱️ 模拟鼠标释放事件...");
            createBtn.dispatchEvent(new MouseEvent('mouseup', {
                button: 0,
                clientX: 450,
                clientY: 350
            }));
            
            console.log("✅ 第一个立方体创建完成");
        }, 1000);
        
        return true;
    }

    // 测试吸附功能
    testSnapping() {
        console.log("🧲 测试吸附功能...");
        
        // 这里可以添加更复杂的吸附测试逻辑
        console.log("📝 吸附测试说明:");
        console.log("1. 手动按住创建按钮");
        console.log("2. 将鼠标移动到现有立方体附近");
        console.log("3. 观察半透明预览和自动吸附效果");
        console.log("4. 释放鼠标完成吸附");
        
        return true;
    }

    // 测试面1限制
    testFace1Restriction() {
        console.log("🚫 测试面1限制规则...");
        console.log("📝 面1限制测试说明:");
        console.log("1. 面1是红色面(X轴正方向)");
        console.log("2. 面1不能与任何其他面重合");
        console.log("3. 尝试将新立方体的任何面靠近现有立方体的红色面");
        console.log("4. 应该看到吸附被阻止");
        
        return true;
    }

    // 测试Z轴面限制
    testZAxisRestriction() {
        console.log("🎨 测试Z轴面限制规则...");
        console.log("📝 Z轴面限制测试说明:");
        console.log("1. 面5是洋红色面(Z轴正方向)");
        console.log("2. 面6是青色面(Z轴负方向)");
        console.log("3. 面5和面6只能与其他立方体的面5和面6吸附");
        console.log("4. 尝试将洋红色/青色面与其他颜色面吸附应该被阻止");
        
        return true;
    }

    // 测试视图切换
    testViewToggle() {
        console.log("🔄 测试视图切换...");
        
        const toggleBtn = document.getElementById('toggle-view');
        if (!toggleBtn) {
            console.error("❌ 未找到切换按钮");
            return false;
        }
        
        console.log("🖱️ 模拟点击切换按钮...");
        toggleBtn.click();
        
        setTimeout(() => {
            console.log("🖱️ 再次切换回原视图...");
            toggleBtn.click();
            console.log("✅ 视图切换测试完成");
        }, 2000);
        
        return true;
    }

    // 运行演示
    runDemo() {
        console.log("🚀 开始 Xifu 功能演示...");
        console.log(`📋 共有 ${this.demoSteps.length} 个演示步骤`);
        
        this.runNextStep();
    }

    // 运行下一个步骤
    runNextStep() {
        if (this.currentStep >= this.demoSteps.length) {
            console.log("🎉 所有演示步骤完成！");
            this.showSummary();
            return;
        }
        
        const step = this.demoSteps[this.currentStep];
        console.log(`\n📍 步骤 ${this.currentStep + 1}: ${step.name}`);
        console.log(`📝 ${step.description}`);
        
        try {
            const result = step.action();
            if (result) {
                console.log(`✅ 步骤 ${this.currentStep + 1} 完成`);
            } else {
                console.log(`⚠️ 步骤 ${this.currentStep + 1} 遇到问题`);
            }
        } catch (error) {
            console.error(`❌ 步骤 ${this.currentStep + 1} 执行失败:`, error);
        }
        
        this.currentStep++;
        
        // 自动进行下一步（除了需要手动操作的步骤）
        if (this.currentStep < 3) {
            setTimeout(() => this.runNextStep(), 2000);
        } else {
            console.log("⏸️ 演示暂停，请手动测试后续功能");
            console.log("💡 输入 demo.continueDemo() 继续演示");
        }
    }

    // 继续演示
    continueDemo() {
        this.runNextStep();
    }

    // 显示总结
    showSummary() {
        console.log("\n📊 Xifu 功能演示总结:");
        console.log("✅ 应用初始化正常");
        console.log("✅ 立方体创建功能可用");
        console.log("✅ 吸附系统已实现");
        console.log("✅ 面编号规则已配置");
        console.log("✅ 视图切换功能正常");
        console.log("\n🎯 建议进一步测试:");
        console.log("1. 手动创建多个立方体");
        console.log("2. 测试各种吸附组合");
        console.log("3. 验证所有面的颜色编码");
        console.log("4. 测试键盘快捷键");
        console.log("5. 在不同设备上测试响应式设计");
    }

    // 获取当前场景信息
    getSceneInfo() {
        const cubeCount = document.getElementById('cube-count');
        const currentMode = document.getElementById('current-mode');
        
        console.log("📊 当前场景信息:");
        console.log(`🎲 立方体数量: ${cubeCount ? cubeCount.textContent : '未知'}`);
        console.log(`👁️ 当前视图: ${currentMode ? currentMode.textContent : '未知'}`);
    }
}

// 创建全局演示实例
if (typeof window !== 'undefined') {
    window.demo = new XifuDemo();
    
    console.log("🎮 Xifu 演示脚本已加载");
    console.log("💡 使用 demo.runDemo() 开始演示");
    console.log("💡 使用 demo.getSceneInfo() 查看场景信息");
}

// 导出供Node.js环境使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = XifuDemo;
}
