export class UIController {
    constructor(app) {
        this.app = app;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Toggle view button
        const toggleViewBtn = document.getElementById('toggle-view');
        toggleViewBtn.addEventListener('click', () => {
            this.app.toggleView();
        });

        // Create cube button - mouse events
        const createCubeBtn = document.getElementById('create-cube');
        
        createCubeBtn.addEventListener('mousedown', (event) => {
            if (event.button === 0) { // Left mouse button
                event.preventDefault();
                this.startCubeCreation();
            }
        });

        createCubeBtn.addEventListener('mouseup', (event) => {
            if (event.button === 0) { // Left mouse button
                event.preventDefault();
                this.stopCubeCreation();
            }
        });

        // Remove mouseleave event - we don't want to stop creation when leaving the button
        // createCubeBtn.addEventListener('mouseleave', () => {
        //     this.stopCubeCreation();
        // });

        // Prevent context menu on create button
        createCubeBtn.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });

        // Global mouse events for cube creation
        document.addEventListener('mouseup', (event) => {
            if (event.button === 0) { // Left mouse button
                this.stopCubeCreation();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleKeyDown(event);
        });

        // Prevent default drag behavior on the create button
        createCubeBtn.addEventListener('dragstart', (event) => {
            event.preventDefault();
        });
    }

    startCubeCreation() {
        if (this.app.cubeManager) {
            this.app.cubeManager.startDragging(this.app.mouse);
            this.updateCreateButtonState(true);
        }
    }

    stopCubeCreation() {
        if (this.app.cubeManager) {
            this.app.cubeManager.stopDragging();
            this.updateCreateButtonState(false);
        }
    }

    updateCreateButtonState(isActive) {
        const createCubeBtn = document.getElementById('create-cube');
        if (isActive) {
            createCubeBtn.textContent = 'Creating Cube... (Release to Place)';
            createCubeBtn.style.background = '#3d8b40';
        } else {
            createCubeBtn.textContent = 'Hold & Drag to Create Cube';
            createCubeBtn.style.background = '#4CAF50';
        }
    }

    handleKeyDown(event) {
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.app.toggleView();
                break;
            
            case 'KeyC':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.clearAllCubes();
                }
                break;
            
            case 'Escape':
                this.stopCubeCreation();
                break;
            
            case 'KeyR':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.resetCamera();
                }
                break;
        }
    }

    clearAllCubes() {
        if (this.app.cubeManager) {
            this.app.cubeManager.clearAll();
        }
    }

    resetCamera() {
        if (this.app.is3D) {
            this.app.camera.position.set(10, 10, 10);
            this.app.camera.lookAt(0, 0, 0);
        } else {
            this.app.camera.position.set(0, 20, 0);
            this.app.camera.lookAt(0, 0, 0);
        }
    }

    // Add visual feedback methods
    showSnapIndicator(_position) {
        // Could add visual indicators for snap positions
        // This is a placeholder for future enhancement
    }

    hideSnapIndicator() {
        // Hide snap indicators
        // This is a placeholder for future enhancement
    }

    updateInstructions(_text) {
        // Update instruction text dynamically
        // This is a placeholder for future enhancement
    }
}
