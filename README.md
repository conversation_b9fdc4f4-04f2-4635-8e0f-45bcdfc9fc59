# Xifu - Three.js 立方体吸附系统

一个基于Three.js的WebGL项目，实现了智能的立方体吸附逻辑系统。

## 功能特性

### 核心功能
- **2D/3D场景切换**: 支持在2D俯视图和3D透视图之间切换
- **拖拽创建立方体**: 按住鼠标左键拖拽创建立方体
- **智能吸附系统**: 实现复杂的面对面吸附逻辑
- **实时预览**: 拖拽时显示半透明预览和吸附提示

### 立方体面编号系统
每个立方体的六个面按以下规则编号：
- **面1**: X轴正方向 (右侧) - 红色
- **面2**: Y轴正方向 (顶部) - 绿色  
- **面3**: X轴负方向 (左侧) - 蓝色
- **面4**: Y轴负方向 (底部) - 黄色
- **面5**: Z轴正方向 (前面) - 洋红色
- **面6**: Z轴负方向 (后面) - 青色

### 吸附规则
1. **面1智能旋转**: 序号1的面接近其他面时，会自动旋转到合适的面进行吸附
   - 面1接近面2 → 旋转使用面4吸附
   - 面1接近面3 → 旋转使用面3吸附
   - 面1接近面4 → 旋转使用面2吸附
   - 面1接近面5 → 旋转使用面6吸附
   - 面1接近面6 → 旋转使用面5吸附
2. **Z轴面限制**: 序号5和序号6（Z轴上的两个面）只能与其他立方体的序号5和序号6面重合
3. **双面吸附**: 当立方体位于两个现有立方体之间时，会拉伸以同时吸附两个面
4. **其他面**: 面2、3、4可以与除面1外的其他面吸附

## 使用方法

### 基本操作
1. **创建立方体**: 按住"Hold & Drag to Create Cube"按钮并拖拽鼠标
2. **选中立方体**: 点击立方体进行选中，选中的立方体会高亮显示并显示变换控制器
3. **变换控制**: 拖拽坐标轴箭头移动立方体，拖拽旋转环旋转立方体
4. **删除立方体**: 选中立方体后按Delete键或Backspace键删除
5. **切换视图**: 点击"Switch to 2D/3D"按钮或按空格键
6. **相机控制**:
   - 鼠标滚轮：缩放
   - 右键拖拽：旋转视角
   - 中键拖拽：平移视角

### 键盘快捷键
- `Delete` / `Backspace`: 删除选中的立方体
- `Space`: 切换2D/3D视图
- `Ctrl+C`: 清除所有立方体
- `Ctrl+R`: 重置相机位置
- `Escape`: 取消当前立方体创建

### 吸附系统
- **实时跟随**: 拖拽立方体时，立方体会实时跟随鼠标移动
- **智能检测**: 系统会自动检测可能的吸附位置，检测距离为2.0单位
- **瞬移吸附**: 当立方体距离有效吸附位置小于1.2单位时，会瞬移到吸附位置
- **视觉反馈**: 吸附时立方体会变为彩色半透明状态，并略微放大(1.05倍)
- **精确放置**: 释放鼠标时，立方体会精确放置在吸附位置

## 技术实现

### 项目结构
```
xifu/
├── src/
│   ├── main.js          # 主应用程序
│   ├── CubeManager.js   # 立方体管理器
│   ├── SnapSystem.js    # 吸附系统算法
│   └── UIController.js  # UI控制器
├── index.html           # 主HTML文件
├── style.css           # 样式文件
└── package.json        # 项目配置
```

### 核心算法
- **面法线计算**: 使用四元数变换计算世界坐标系下的面法线
- **吸附检测**: 基于面法线点积判断面的对齐程度
- **距离优化**: 选择距离最近的有效吸附位置
- **碰撞检测**: 防止立方体重叠

## 最新更新 (v2.0)

### 🆕 新增功能 (v2.1)
1. **地面放置系统**: 立方体优先放置在y=0水平面上，提供稳定的基础放置
2. **选中与变换控制**: 点击立方体可选中(高亮显示)，显示三轴坐标轴控制器
3. **三轴变换控制器**: 拖拽红/绿/蓝色坐标轴进行移动和旋转，拖拽时忽略吸附
4. **智能旋转吸附**: 面1接近其他面时自动旋转到合适的面进行吸附
5. **双面吸附拉伸**: 立方体可在两个现有立方体间拉伸，同时吸附两个面
6. **删除功能**: 按Delete键删除选中的立方体

### 🐛 已修复的问题 (v1.1)
1. **立方体跟随鼠标移动**: 立方体现在能在2D和3D空间中实时跟随鼠标移动
2. **鼠标离开按钮问题**: 移除了mouseleave事件，只有松开鼠标左键才会放置立方体
3. **3D定位改进**: 添加了智能的3D位置计算，支持多种交集检测策略

### 🎯 增强功能
- **瞬移吸附**: 当距离小于1.2单位时，立方体瞬移到吸附位置
- **视觉反馈**: 吸附时立方体变为彩色半透明并略微放大
- **自适应定位**: 根据场景内容和相机位置智能计算立方体位置

## 开发环境

### 依赖
- Three.js ^0.158.0
- Vite ^5.0.0 (开发工具)

### 安装和运行
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 测试页面
- `http://localhost:5173/` - 主应用
- `test-new-features.html` - 新功能测试页面 (推荐)
- `test-fixes.html` - 修复验证页面
- `verify.html` - 完整功能验证页面

## 测试步骤

### 基础功能测试
1. **立方体创建测试**:
   - 按住"Hold & Drag to Create Cube"按钮
   - 移动鼠标，观察立方体是否跟随鼠标移动
   - 释放鼠标，确认立方体被放置

2. **吸附功能测试**:
   - 创建第一个立方体作为基础
   - 创建第二个立方体，将其移动到第一个立方体附近
   - 观察当距离小于1.2单位时，立方体是否瞬移到吸附位置
   - 观察吸附时的视觉反馈（颜色变化、大小变化）

3. **面规则测试**:
   - 测试面1(红色)不能与任何面吸附的规则
   - 测试面5/6(洋红色/青色)只能与面5/6吸附的规则
   - 测试其他面的正常吸附功能

### 高级功能测试
4. **视图切换测试**:
   - 在2D和3D视图之间切换
   - 确认在不同视图下吸附功能正常工作

5. **多立方体测试**:
   - 创建多个立方体
   - 测试复杂的吸附组合
   - 验证吸附优先级（选择最近的有效吸附点）

## 扩展功能建议

1. **增强吸附算法**:
   - 支持旋转对齐
   - 多面同时吸附
   - 复杂几何体支持

2. **可视化改进**:
   - 吸附点高亮显示
   - 面编号标签
   - 动画过渡效果

3. **交互优化**:
   - 触摸设备支持
   - 撤销/重做功能
   - 保存/加载场景

4. **物理引擎集成**:
   - 重力模拟
   - 碰撞响应
   - 稳定性检测

## 许可证

MIT License
