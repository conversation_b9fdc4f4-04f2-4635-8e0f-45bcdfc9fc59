<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xifu - Three.js Cube Snapping System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <!-- UI Controls -->
        <div id="ui-panel">
            <div class="control-group">
                <button id="toggle-view" class="btn">Switch to 2D</button>
                <button id="create-cube" class="btn create-btn">Hold & Drag to Create Cube</button>
                <button id="clear-selection" class="btn">Clear Selection</button>
            </div>
            <div class="info-panel">
                <h3>Instructions:</h3>
                <ul>
                    <li>Hold and drag the "Create Cube" button to generate cubes</li>
                    <li>Click on cubes to select them, press Delete to remove</li>
                    <li>Face 1 (X+) smart rotation: auto-rotates when approaching other faces</li>
                    <li>Faces 5 & 6 (Z±) can only snap to other faces 5 & 6</li>
                    <li>Dual-face snapping: cubes stretch between two existing cubes</li>
                    <li>Use mouse wheel to zoom, right-click to rotate, arrow keys to pan</li>
                </ul>
            </div>
        </div>

        <!-- Three.js Canvas Container -->
        <div id="canvas-container"></div>

        <!-- Debug Info -->
        <div id="debug-info">
            <div>Mode: <span id="current-mode">3D</span></div>
            <div>Cubes: <span id="cube-count">0</span></div>
            <div>Mouse: <span id="mouse-pos">0, 0</span></div>
        </div>
    </div>

    <script type="module" src="src/main.js"></script>
</body>
</html>
